import React, { useState, useEffect, useContext, useCallback } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import validations from "./schemas/validations";
import form from "./schemas/form";
import initialValues from "./schemas/initialValues";
import { Questions } from "./components/Questions";
import { toast } from "react-toastify";
import { ApiServiceContext } from "context";
import { validateTotalScore } from "utils/helperFunctions/validateTotalScore";
import { DeleteModal } from "../DeleteModal/DeleteModal";
import { GenerateTopics } from "./components/GenerateTopics";
import AssignmentButtons from "./components/AssignmentButtons";
import Loader from "components/atoms/Loader/Loader";
import CustomizedSteppers from "./CustomizedSteppers/CustomizedSteppers";
import FloatingArrow from "components/atoms/FloatingArrow/FloatingArrow";
import TopicGenerationRequestedScreen from "../TopicGenerationRequestedScreen/TopicGenerationRequestedScreen";

export function TestForm({ viewOnly = false, formType = "add" }) {
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);
  useEffect(() => {
    if (!loading && !apiService) {
      handleLogin(window.location.href);
    }
  }, [apiService, loading]);

  const { assignmentId } = useParams();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const OPERATION = queryParams.get("operation");
  const folderId = queryParams.get("folderId");

  const [initialData, setInitialData] = useState(initialValues);
  const [activeStep, setActiveStep] = useState(0);
  const [isAssignmentEdit, setIsAssignmentEdit] = useState(false);
  const { formId, formField } = form;
  const currentValidation = validations[activeStep];
  const navigate = useNavigate();

  const [isValidScore, setIsValidScore] = useState(false);

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteAssignmentLoader, setDeleteAssignmentLoader] = useState(false);
  const [error, setError] = useState(null);

  const [newAssignmentId, setNewTestId] = useState("");
  const [isTopicsGenerated, setIsTopicsGenerated] = useState(false);
  const [isTopicsLoading, setIsTopicsLoading] = useState(false);
  const [topicsData, setTopicsData] = useState(null);
  const [isTopicGenerationRequested, setIsTopicGenerationRequested] =
    useState(false);

  const [isLoading, setIsLoading] = useState(true);

  const [errorList, setErrorList] = useState([]);

  const [rubricLoading, setRubricLoading] = useState(false);
  const [isRubricGenerated, setIsRubricGenerated] = useState(false);

  const [isShowRubricScreen, setIsShowRubricScreen] = useState(false);

  const [hasRubricErrors, setHasRubricErrors] = useState(false);

  const [isFormErrors, setIsFormErrors] = useState(false);

  const [isNewQuestionAdded, setIsNewQuestionAdded] = useState(false);
  const [isValuesChanged, setIsValuesChanged] = useState(false);

  const [isRubricButtonClicked, setIsRubricButtonClicked] = useState(false);

  const [isRubricFromFiles, setIsRubricFromFiles] = useState(false);
  const [isAutoFillAssignment, setIsAutoFillAssignment] = useState(false);

  const steps = getSteps(isAssignmentEdit);

  function getSteps(isAssignmentEdit) {
    const steps = ["Assignment Form", "Rubric", "Topics"];

    if (isAssignmentEdit) {
      return steps.filter((step) => step !== "Topics");
    }

    if (isShowRubricScreen) {
      return steps.filter((step) => step !== "Rubric");
    }

    return steps;
  }

  function getStepContent(stepIndex, formData) {
    switch (stepIndex) {
      case 0:
      case 1:
        return (
          <Questions
            formType={formType}
            formData={formData}
            viewOnly={viewOnly}
            isValidScore={isValidScore}
            setIsValidScore={setIsValidScore}
            errorList={errorList}
            checkFieldErrors={checkFieldErrors}
            rubricLoading={rubricLoading}
            setRubricLoading={setRubricLoading}
            isRubricGenerated={isRubricGenerated}
            setIsRubricGenerated={setIsRubricGenerated}
            hasRubricErrors={hasRubricErrors}
            isShowRubricScreen={isShowRubricScreen}
            setIsShowRubricScreen={setIsShowRubricScreen}
            isNewQuestionAdded={isNewQuestionAdded}
            setIsNewQuestionAdded={setIsNewQuestionAdded}
            isValuesChanged={isValuesChanged}
            setIsValuesChanged={setIsValuesChanged}
            setActiveStep={setActiveStep}
            isRubricButtonClicked={isRubricButtonClicked}
            setIsRubricButtonClicked={setIsRubricButtonClicked}
            isRubricFromFiles={isRubricFromFiles}
            setIsRubricFromFiles={setIsRubricFromFiles}
            scrollToBottom={scrollToBottom}
            activeStep={activeStep}
            isAutoFillAssignment={isAutoFillAssignment}
            setIsAutoFillAssignment={setIsAutoFillAssignment}
          />
        );
      case 2:
        return isTopicGenerationRequested ? (
          <TopicGenerationRequestedScreen />
        ) : (
          <GenerateTopics
            formType={formType}
            isTopicsGenerated={isTopicsGenerated}
            topicsData={topicsData}
            generateTopics={generateTopics}
            isTopicsLoading={isTopicsLoading}
            setIsTopicsLoading={setIsTopicsLoading}
          />
        );
      default:
        return null;
    }
  }
  const fetchData = async () => {
    if (
      assignmentId !== ":assignmentId" &&
      (formType === "edit" || formType === "view")
    ) {
      try {
        const data = await apiService.getAssignment(assignmentId);
        const hasTopics = data?.questions?.some((question) =>
          question?.topics?.some((topic) => topic?.topics?.length > 0)
        );

        if (hasTopics) {
          setIsTopicsGenerated(true);
          setTopicsData(data);
        }
        setInitialData(data);

        if (!data) {
          toast.error("Failed to load assignment data");
        }
        setIsLoading(false);
      } catch (err) {
        toast.error("Failed to load assignment data");
        setIsLoading(false);
      }
    } else if (formType === "edit" || formType === "view") {
      toast.error("Select the assignment first");
      setIsLoading(false);
    } else {
      setActiveStep(0);
      setInitialData(initialValues);
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchData();
  }, [assignmentId, navigate, apiService]);

  useEffect(() => {
    if (OPERATION === "view") {
      setIsLoading(true);
      setIsAssignmentEdit(false);
    } else if (OPERATION === "edit") {
      setIsAssignmentEdit(true);
    }
  }, [OPERATION]);

  const handleBack = () => setActiveStep(activeStep - 1);
  const handleNavigateToGradeAssignment = () =>
    navigate(`/assignments/${newAssignmentId}?operation=grade`);

  const checkFieldErrors = useCallback(
    (values) => {
      const errorIndices = [];

      values?.length > 0 &&
        values?.forEach((item, index) => {
          if (
            item.questionNumber === 0 ||
            item.questionScore === 0 ||
            item.question === "" ||
            (hasRubricErrors && activeStep === 1 && item.questionRubric === "")
          ) {
            errorIndices.push(index);
          }
        });

      // Only update if the state changes
      if (hasRubricErrors && errorIndices.length === 0) {
        setHasRubricErrors(false);
      }

      setErrorList((prev) => {
        // Only update if the new list differs from the previous one
        const isDifferent =
          JSON.stringify(prev) !== JSON.stringify(errorIndices);
        return isDifferent ? errorIndices : prev;
      });
    },
    [hasRubricErrors]
  );

  const createAssignment = async (values, actions) => {
    if (!validateTotalScore(values)) {
      toast.error("The total question score does not match the total score.");
      actions.setSubmitting(false);
      return;
    }
    try {
      if (assignmentId && !viewOnly) {
        await apiService.editAssignment(assignmentId, values);
        toast.success("Assignment edited successfully");
        actions.setSubmitting(false);
        actions.resetForm();
        setIsLoading(true);
        navigate(`/assignments/${assignmentId}?operation=view`);
        fetchData();
      } else if (!assignmentId) {
        // Add folderId to values if it exists
        const assignmentData = {
          ...values,
          ...(folderId && { folderId })
        };

        const newTest = await apiService.createNewAssignments(assignmentData);
        setNewTestId(newTest?.id);
        toast.success("Assignment created successfully");
        actions.setSubmitting(false);
        actions.resetForm();
        setActiveStep(2);
      }
    } catch (err) {
      toast.error("Assignment already exist or Error in creating assignment");
      navigate("/assignments");
      actions.setSubmitting(false);
      actions.resetForm();
      setActiveStep(0);
    }
  };

  const generateTopics = async () => {
    let tempAssignmentId;

    if (formType === "add") {
      tempAssignmentId = newAssignmentId;
    } else if (formType === "view" && !isTopicsGenerated) {
      tempAssignmentId = assignmentId;
    }

    if (!tempAssignmentId) {
      toast.error("Error in Generating Topics");
      setActiveStep(2);
      setIsTopicsLoading(false);
      setIsTopicsGenerated(false);
      return;
    }
    try {
      const topicsResult = await apiService.generateTopics(tempAssignmentId);

      if (topicsResult !== undefined) {
        toast.success(
          topicsResult.message || "Topics Generation requested successfully"
        );
        setIsTopicsGenerated(true);
        setIsTopicsLoading(false);
        setIsTopicGenerationRequested(true);
        setActiveStep(2);
      } else {
        toast.error("Error in Generating Topics, Please try after some time.");
        setIsTopicsLoading(false);
        setIsTopicsGenerated(false);
        navigate("/assignments");
      }
    } catch (err) {
      toast.error("Error in Generating Topics, Please try after some time.");
      setIsTopicsLoading(false);
      setIsTopicsGenerated(false);
      navigate("/assignments");
    }
  };

  const handleSubmit = (values, actions) => {
    if (!isValidScore) {
      toast.error(
        "The total question score does not match the given total score"
      );
      actions.setSubmitting(false);
      return;
    }

    if (isFormErrors) {
      actions.setSubmitting(false);
      return;
    }

    createAssignment(values, actions);
  };

  const handleDeleteAssignment = async () => {
    if (assignmentId) {
      setDeleteAssignmentLoader(true);
      try {
        await apiService.deleteAssignment(assignmentId);
        toast.success("Assignment Deleted Successfully");
        navigate("/assignments");
      } catch (err) {
        setError(err);
      } finally {
        setDeleteAssignmentLoader(false);
        setIsDeleteModalOpen(false);
      }
    }
  };

  const handleDeleteTestModal = () => {
    setIsDeleteModalOpen(true);
  };

  const handleAssignmentEdit = () => {
    activeStep === 2 && setActiveStep(0);
    setIsAssignmentEdit(true);
    navigate(`/assignments/${assignmentId}?operation=edit`);
  };

  const handleCancel = () => {
    setIsLoading(true);
    setIsAssignmentEdit(false);
    navigate(`/assignments/${assignmentId}?operation=view`);
    fetchData();
  };

  const handleViewAssignment = () => {
    setIsLoading(true);
    setIsRubricButtonClicked(false);
    navigate(`/assignments/${newAssignmentId}?operation=view`);
    fetchData();
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const scrollToBottom = () => {
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: "smooth",
    });
  };

  const generateRubricChecks = (values) => {
    // Check for invalid fields and return early if any are found
    const isAnyFieldEmptyOrInvalid =
      values.name.length < 5 ||
      values.name.length > 100 ||
      values.totalScore <= 0 ||
      values.class <= 0 ||
      values.subjectName === "" ||
      values.sectionList.length === 0;

    if (isAnyFieldEmptyOrInvalid) {
      toast.error("Please fill all the required fields!");
      return true;
    }

    const hasInvalidFields = values?.questions.some(
      ({ question, questionScore, questionNumber }) =>
        !question || questionScore <= 0 || !questionNumber
    );

    if (hasInvalidFields) {
      setHasRubricErrors(true);
      toast.error("Please fill all the required fields !");
      return true;
    }

    // Check if question numbers are unique
    const questionNumbers = values?.questions.map(
      ({ questionNumber }) => questionNumber
    );
    const uniqueNumbers = new Set(questionNumbers);

    if (uniqueNumbers.size !== questionNumbers.length) {
      toast.error("Each question must have a unique question number.");
      return true;
    }

    if (!validateTotalScore(values)) {
      toast.error("The total question score does not match the total score.");
      return true;
    }

    return false;
  };

  useEffect(() => {
    const handleBackNavigation = () => {
      navigate(`/assignments`, { replace: true });
    };

    window.addEventListener("popstate", handleBackNavigation);

    return () => {
      window.removeEventListener("popstate", handleBackNavigation);
    };
  }, [navigate, OPERATION]);

  if (isLoading) {
    return <Loader fullScreen message="Don't Reload Page" />;
  }

  return (
    <DashboardLayout>
      <DashboardNavbar
        breadCrumbText={
          formType == "add"
            ? "Create New Assignment"
            : formType == "edit"
            ? "Edit Assignment : " + initialData?.name || assignmentId
            : "View Assignment : " + initialData?.name || assignmentId
        }
      />
      <DeleteModal
        isModalOpen={isDeleteModalOpen}
        setIsModalOpen={setIsDeleteModalOpen}
        handleChange={handleDeleteAssignment}
        title="Delete Assignment"
        loader={deleteAssignmentLoader}
      />
      <MDBox sx={{ mb: 4, position: "relative" }}>
        {formType !== "add" && (
          <MDBox
            sx={{
              position: "absolute",
              top: -55,
              right: 0,
              zIndex: 999,
              display: "flex",
              alignItems: "center",
              gap: 2,
            }}
          >
            {!isAssignmentEdit ? (
              <>
                <MDButton
                  variant="gradient"
                  color="secondary"
                  onClick={() =>
                    navigate(`/assignments/${assignmentId}?operation=grade`)
                  }
                >
                  Grade Assignment
                </MDButton>
                <MDButton
                  variant="gradient"
                  color="info"
                  onClick={() => handleAssignmentEdit()}
                >
                  Edit
                </MDButton>
                <MDButton
                  variant="outlined"
                  color="error"
                  onClick={() => handleDeleteTestModal()}
                >
                  Delete
                </MDButton>
              </>
            ) : (
              <MDButton
                variant="outlined"
                color="error"
                onClick={() => handleCancel()}
              >
                Cancel
              </MDButton>
            )}
          </MDBox>
        )}
        <Grid
          justifyContent="center"
          alignItems="center"
          sx={{ height: "100%", mt: 1 }}
        >
          <Grid item xs={12} lg={8}>
            <Formik
              initialValues={initialData}
              validationSchema={currentValidation}
              onSubmit={handleSubmit}
              enableReinitialize={true}
            >
              {({ values, errors, touched, isSubmitting, setFieldValue }) => (
                <Form id={formId} autoComplete="off">
                  <Card sx={{ mt: 2 }}>
                    <CustomizedSteppers
                      activeStep={activeStep}
                      steps={steps}
                      formType={formType}
                    />

                    <MDBox
                      sx={{
                        minHeight: "calc(100vh - 290px)",
                        overflow: "auto",
                        display: "flex",
                        flexDirection: "column",
                      }}
                    >
                      <MDBox
                        sx={{
                          flex: 1,
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "space-between",
                        }}
                      >
                        {getStepContent(activeStep, {
                          values,
                          touched,
                          formField,
                          errors,
                          setFieldValue,
                          viewOnly,
                        })}
                        <MDBox
                          width="100%"
                          display="flex"
                          justifyContent="space-between"
                          p={5}
                          sx={{
                            zIndex: 50,
                          }}
                        >
                          <AssignmentButtons
                            activeStep={activeStep}
                            formType={formType}
                            isSubmitting={isSubmitting}
                            handleBack={handleBack}
                            setActiveStep={setActiveStep}
                            errors={errors}
                            handleViewAssignment={handleViewAssignment}
                            isRubricGenerated={isRubricGenerated}
                            handleNavigateToGradeAssignment={
                              handleNavigateToGradeAssignment
                            }
                            values={values}
                            setIsFormErrors={setIsFormErrors}
                            isShowRubricScreen={isShowRubricScreen}
                            setIsShowRubricScreen={setIsShowRubricScreen}
                            scrollToTop={scrollToTop}
                            generateRubricChecks={generateRubricChecks}
                            hasRubricErrors={hasRubricErrors}
                            setHasRubricErrors={setHasRubricErrors}
                            isValuesChanged={isValuesChanged}
                            isRubricButtonClicked={isRubricButtonClicked}
                            setIsRubricButtonClicked={setIsRubricButtonClicked}
                            isRubricFromFiles={isRubricFromFiles}
                            setIsRubricFromFiles={setIsRubricFromFiles}
                            isAutoFillAssignment={isAutoFillAssignment}
                            setIsAutoFillAssignment={setIsAutoFillAssignment}
                          />
                        </MDBox>
                      </MDBox>
                    </MDBox>
                  </Card>
                </Form>
              )}
            </Formik>
          </Grid>
        </Grid>
      </MDBox>
      <FloatingArrow
        scrollToTop={scrollToTop}
        scrollToBottom={scrollToBottom}
      />
    </DashboardLayout>
  );
}
