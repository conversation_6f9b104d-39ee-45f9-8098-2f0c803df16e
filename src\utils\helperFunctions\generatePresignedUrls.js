import AWS from "aws-sdk";

const s3 = new AWS.S3({
  forcePathStyle: false,
  endpoint: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_ENDPOINT,
  region: process.env.REACT_APP_AWS_REGION,
  credentials: {
    accessKeyId: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_ACCESS_KEY,
    secretAccessKey: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_SECRET_KEY,
  },
});

export const generatePresignedUrls = (imageUrls) => {
  if (!imageUrls) return;
  const presignedUrls = imageUrls.map((imageKey) => {
    const params = {
      Bucket: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_BUCKET,
      Key: imageKey,
      Expires: 60,
    };
    return s3.getSignedUrl("getObject", params);
  });
  return presignedUrls;
};
