import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import Loader from "components/atoms/Loader/Loader";

export function MoveToFolderModal({
  open,
  onClose,
  onMove,
  foldersList,
  assignmentTitle,
  currentFolderId,
  isLoading = false,
}) {
  const [selectedFolderId, setSelectedFolderId] = useState(null);

  const handleMove = () => {
    if (onMove) {
      onMove(selectedFolderId);
    }
  };

  const handleClose = () => {
    setSelectedFolderId(null);
    onClose();
  };

  // Filter out the current folder from the list
  const availableFolders = foldersList?.filter(
    (folder) => folder.id !== currentFolderId
  ) || [];

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <MDTypography variant="h5" fontWeight="medium">
          Move Assignment
        </MDTypography>
      </DialogTitle>
      
      <DialogContent>
        <MDBox mb={2}>
          <MDTypography variant="body2" color="text">
            Move "{assignmentTitle}" to:
          </MDTypography>
        </MDBox>

        <FormControl fullWidth>
          <InputLabel id="folder-select-label">Select Destination</InputLabel>
          <Select
            labelId="folder-select-label"
            value={selectedFolderId}
            label="Select Destination"
            onChange={(e) => setSelectedFolderId(e.target.value)}
            disabled={isLoading}
            sx={{
              minHeight: "56px",
              "& .MuiSelect-select": {
                minHeight: "1.4375em",
                display: "flex",
                alignItems: "center",
              },
            }}
          >
            {/* Root option - only show if not already at root */}
            {currentFolderId && (
              <MenuItem value="">
                <MDTypography variant="body2" fontWeight="medium">
                  📁 Root (All Assignments)
                </MDTypography>
              </MenuItem>
            )}
            
            {/* Available folders */}
            {availableFolders.map((folder) => (
              <MenuItem key={folder.id} value={folder.id}>
                <MDTypography variant="body2">
                  📂 {folder.name}
                </MDTypography>
              </MenuItem>
            ))}
            
            {/* No folders available message */}
            {availableFolders.length === 0 && !currentFolderId && (
              <MenuItem disabled>
                <MDTypography variant="body2" color="text">
                  No folders available
                </MDTypography>
              </MenuItem>
            )}
          </Select>
        </FormControl>
      </DialogContent>

      <DialogActions>
        <MDButton
          variant="outlined"
          color="secondary"
          onClick={handleClose}
          disabled={isLoading}
        >
          Cancel
        </MDButton>
        <MDButton
          variant="gradient"
          color="info"
          onClick={handleMove}
          disabled={selectedFolderId === null || isLoading}
          sx={{ minWidth: "100px" }}
        >
          {isLoading ? <Loader loaderColor="white" /> : "Move"}
        </MDButton>
      </DialogActions>
    </Dialog>
  );
}
