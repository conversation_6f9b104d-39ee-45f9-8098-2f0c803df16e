/**
=========================================================
* Material Dashboard 2 PRO React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-pro-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// Material Dashboard 2 PRO React components
import MDBox from "components/atoms/MDBox";
import { truncateText } from "utils/helperFunctions/truncateText";
import { Tooltip } from "@mui/material";
function DataTableBodyCell({
  noBorder = false,
  align = "left",
  children,
  isVisible = true,
  isEmailColumn,
}) {
  return (
    isVisible && (
      <MDBox
        component="td"
        textAlign={align}
        py={1}
        px={3}
        sx={({
          palette: { light },
          typography: { size },
          borders: { borderWidth },
        }) => ({
          fontSize: size.sm,
          borderBottom: noBorder
            ? "none"
            : `${borderWidth[1]} solid ${light.main}`,
        })}
      >
        <MDBox
          display="inline-block"
          width="max-content"
          color="text"
          sx={{
            verticalAlign: "middle",
            textTransform: isEmailColumn ? "lowercase" : "capitalize",
          }}
        >
          <>
            {typeof children === "string" && children.length > 30 ? (
              <Tooltip title={children}>
                <span style={{ cursor: "pointer" }}>
                  {truncateText(children, 30)}
                </span>
              </Tooltip>
            ) : (
              children
            )}
          </>
        </MDBox>
      </MDBox>
    )
  );
}

DataTableBodyCell.propTypes = {
  children: PropTypes.node.isRequired,
  noBorder: PropTypes.bool,
  align: PropTypes.oneOf(["left", "right", "center"]),
  isVisible: PropTypes.oneOf([false, true]),
  isEmailColumn: PropTypes.oneOf([false, true]),
  isSubmissionStatus: PropTypes.oneOf([false, true]),
};

export default DataTableBodyCell;
