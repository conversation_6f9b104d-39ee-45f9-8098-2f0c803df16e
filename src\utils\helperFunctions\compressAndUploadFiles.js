import Compressor from "compressorjs";
import AWS from "aws-sdk";

export const compressAndUploadFiles = async (
  files,
  fileLocationGenerator,
  signal = null,
  tmpFileListRef = null
) => {
  const s3 = new AWS.S3({
    forcePathStyle: false,
    credentials: {
      accessKeyId: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_ACCESS_KEY,
      secretAccessKey: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_SECRET_KEY,
    },
    region: process.env.REACT_APP_AWS_REGION,
    endpoint: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_ENDPOINT,
  });

  let tmpFileList = [];
  let errorFiles = [];
  const BATCH_SIZE = 20;

  // Convert files to array for easier handling
  const filesArray = Array.from(files);

  // Process files in batches
  for (let i = 0; i < filesArray.length; i += BATCH_SIZE) {
    const batch = filesArray.slice(i, i + BATCH_SIZE);

    const batchPromises = batch.map((file, batchIndex) => {
      const actualIndex = i + batchIndex;
      const fileLocation = fileLocationGenerator(file, actualIndex);

      return new Promise((resolve, reject) => {
        // Check for cancellation before starting compression
        if (signal?.aborted) {
          reject(new DOMException("Operation was aborted.", "AbortError"));
          return;
        }

        new Compressor(file, {
          quality: 0.9, // Slightly better quality, still fast
          maxWidth: 1500, // Reasonable limit
          maxHeight: 2000, // Reasonable limit
          success(compressedFile) {
            // Check for cancellation after compression
            if (signal?.aborted) {
              reject(new DOMException("Operation was aborted.", "AbortError"));
              return;
            }

            const params = {
              Bucket: process.env.REACT_APP_DIGITAL_OCEAN_SPACES_BUCKET,
              Key: fileLocation,
              Body: compressedFile,
            };

            const uploadRequest = s3.upload(params);

            // Add cancellation listener for the upload
            signal?.addEventListener("abort", () => {
              uploadRequest.abort();
              reject(new DOMException("Upload was aborted.", "AbortError"));
            });

            uploadRequest.send((err) => {
              if (err) {
                errorFiles.push(file.name);
                reject(err);
              } else {
                tmpFileList.push(fileLocation);

                if (tmpFileListRef?.current) {
                  tmpFileListRef.current.push(fileLocation);
                }

                resolve();
              }
            });
          },
          error(err) {
            errorFiles.push(file.name);
            reject(err);
          },
        });
      });
    });

    try {
      await Promise.allSettled(batchPromises);
    } catch (err) {
      if (err.name === "AbortError") {
        return { success: false, tmpFileList, errorFiles };
      } else {
      }
    }

    // Check for abortion after each batch
    if (signal?.aborted) {
      return { success: false, tmpFileList, errorFiles };
    }
  }

  // Return results
  if (errorFiles.length > 0) {
    return { success: false, tmpFileList, errorFiles };
  }

  tmpFileList.sort(); // Sort the file list alphabetically

  return { success: true, tmpFileList, errorFiles };
};
