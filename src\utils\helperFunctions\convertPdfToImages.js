import * as pdfjsLib from "pdfjs-dist/legacy/build/pdf";
import pdfjsWorker from "pdfjs-dist/legacy/build/pdf.worker.entry";

// Set worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export const convertPdfToImages = async (pdfFile) => {
  try {
    const pdf = await pdfjsLib.getDocument(URL.createObjectURL(pdfFile))
      .promise;
    const imageBlobs = [];

    // Process pages in smaller batches to avoid memory issues but still get speed benefits
    const PAGE_BATCH_SIZE = 10; // Conservative batch size

    for (let i = 1; i <= pdf.numPages; i += PAGE_BATCH_SIZE) {
      const batchEnd = Math.min(i + PAGE_BATCH_SIZE - 1, pdf.numPages);
      const batchPromises = [];

      // Create promises for current batch
      for (let pageNum = i; pageNum <= batchEnd; pageNum++) {
        const pagePromise = (async () => {
          try {
            const page = await pdf.getPage(pageNum);
            const viewport = page.getViewport({ scale: 1.5 }); // Reduced scale for stability

            const canvas = document.createElement("canvas");
            canvas.height = viewport.height;
            canvas.width = viewport.width;
            const context = canvas.getContext("2d");

            // Render page
            await page.render({ canvasContext: context, viewport }).promise;

            // Convert to blob
            const imageBlob = await new Promise((resolve, reject) => {
              canvas.toBlob((blob) => {
                if (blob) {
                  resolve(blob);
                } else {
                  reject(
                    new Error(`Failed to convert page ${pageNum} to blob`)
                  );
                }
              }, "image/png");
            });

            const imageFile = new File([imageBlob], `page-${pageNum}.png`, {
              type: "image/png",
            });

            // Clean up
            page.cleanup();

            return { pageNum, imageFile };
          } catch (error) {
            throw error;
          }
        })();

        batchPromises.push(pagePromise);
      }

      // Wait for current batch to complete
      try {
        const batchResults = await Promise.all(batchPromises);

        // Sort by page number and add to results
        batchResults
          .sort((a, b) => a.pageNum - b.pageNum)
          .forEach((result) => imageBlobs.push(result.imageFile));
      } catch (error) {
        throw error;
      }
    }

    // Clean up PDF document
    pdf.destroy();

    return imageBlobs;
  } catch (error) {
    throw error;
  }
};
