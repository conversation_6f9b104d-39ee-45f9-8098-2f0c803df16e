import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import Card from "@mui/material/Card";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Breadcrumbs,
  Chip,
} from "@mui/material";
import FolderIcon from "@mui/icons-material/Folder";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import HomeIcon from "@mui/icons-material/Home";
import DescriptionIcon from "@mui/icons-material/Description";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDInput from "components/atoms/MDInput";
import MDTypography from "components/atoms/MDTypography";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import DataTable from "examples/Tables/DataTable";
import Loader from "components/atoms/Loader/Loader";
import { ApiServiceContext } from "context";
import { AssignmentTableHeader } from "constants/AssignmentTableHeader/AssignmentTableHeader";
import { TableInfo } from "components/molecules/TableInfo/TableInfo";

export function Assignments() {
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);
  useEffect(() => {
    if (!loading && !apiService) {
      handleLogin(window.location.href);
    }
  }, [apiService, loading]);
  const [allAssignments, setAllAssignments] = useState([]);
  const [allFolders, setAllFolders] = useState([]);
  const [tableData, setTableData] = useState({});
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Folder navigation state
  const [currentFolderId, setCurrentFolderId] = useState(null);
  const [breadcrumbPath, setBreadcrumbPath] = useState([]);

  // Create folder modal state
  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchAndSetData = async () => {
      try {
        const [result, foldersResult] = await Promise.all([
          apiService.getAllAssignments(),
          apiService.getAllFolders(),
        ]);

        setAllAssignments(result);
        setAllFolders(foldersResult);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchAndSetData();
  }, [apiService]);

  // Custom table header that includes icons for folders and assignments
  const getTableColumns = () => {
    return AssignmentTableHeader.map((column) => {
      if (column.accessor === "type") {
        return {
          ...column,
          Cell: ({ row }) => {
            const isFolder = row.original.isFolder;
            return (
              <MDBox display="flex" alignItems="center" justifyContent="center">
                {isFolder ? (
                  <FolderIcon color="primary" />
                ) : (
                  <DescriptionIcon color="action" />
                )}
              </MDBox>
            );
          },
        };
      }
      if (column.accessor === "name") {
        return {
          ...column,
          Cell: ({ row }) => {
            return (
              <MDTypography variant="button" color="dark" fontWeight="regular">
                {row.original.name}
              </MDTypography>
            );
          },
        };
      }
      return column;
    });
  };

  // Prepare table data based on current folder
  useEffect(() => {
    const prepareTableData = () => {
      if (currentFolderId === null) {
        // Show folders and assignments without folderId
        const foldersAsRows = allFolders.map((folder) => ({
          ...folder,
          isFolder: true,
          name: folder.name,
          type: "folder", // Add type field for the type column
          icon: "", // Empty string so custom Cell renderer is used
          class: "—",
          subjectName: "—",
          totalScore: "—",
          sectionList: ["—"],
        }));

        const unassignedAssignments = allAssignments
          .filter((assignment) => !assignment.folderId)
          .map((assignment) => ({
            ...assignment,
            type: "document", // Add type field for the type column
            icon: "", // Empty string so custom Cell renderer is used
          }));

        const combinedRows = [...foldersAsRows, ...unassignedAssignments];

        setTableData({
          columns: getTableColumns(),
          rows: combinedRows,
        });
      } else {
        // Show assignments in the current folder
        const folderAssignments = allAssignments
          .filter((assignment) => assignment.folderId === currentFolderId)
          .map((assignment) => ({
            ...assignment,
            type: "document", // Add type field for the type column
            icon: "", // Empty string so custom Cell renderer is used
          }));

        setTableData({
          columns: getTableColumns(),
          rows: folderAssignments,
        });
      }
    };

    prepareTableData();
  }, [allAssignments, allFolders, currentFolderId]);

  const handleNavigateToGradeAssignment = (rowValues) => {
    // Find the original row data from our table data
    const originalRow = tableData.rows.find((row) => row.id === rowValues.id);

    console.log("Row clicked:", {
      rowValues,
      originalRow,
      isFolder: originalRow?.isFolder,
    });

    // Check if it's a folder first
    if (originalRow?.isFolder) {
      console.log("Navigating to folder:", originalRow.name);
      // Navigate into folder - no API call, just local state update
      handleFolderClick(originalRow);
      return; // Exit early to prevent any navigation
    }

    // Only navigate to grade assignment if it's not a folder
    if (rowValues?.id && !originalRow?.isFolder) {
      console.log("Navigating to assignment:", rowValues.id);
      navigate(`/assignments/${rowValues.id}?operation=grade`);
    }
  };

  const handleFolderClick = (folder) => {
    setCurrentFolderId(folder.id);
    setBreadcrumbPath([
      ...breadcrumbPath,
      { id: folder.id, name: folder.name },
    ]);
  };

  const handleBackClick = () => {
    if (breadcrumbPath.length > 0) {
      const newPath = [...breadcrumbPath];
      newPath.pop();
      setBreadcrumbPath(newPath);

      if (newPath.length === 0) {
        setCurrentFolderId(null);
      } else {
        setCurrentFolderId(newPath[newPath.length - 1].id);
      }
    }
  };

  const handleBreadcrumbClick = (index) => {
    if (index === -1) {
      // Clicked on "All Assignments"
      setCurrentFolderId(null);
      setBreadcrumbPath([]);
    } else {
      // Clicked on a folder in breadcrumb
      const newPath = breadcrumbPath.slice(0, index + 1);
      setBreadcrumbPath(newPath);
      setCurrentFolderId(newPath[newPath.length - 1].id);
    }
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    setIsCreatingFolder(true);

    try {
      const folderData = {
        name: newFolderName.trim(),
      };

      const newFolder = await apiService.createNewFolder(folderData);
      setAllFolders([...allFolders, newFolder]);
      setNewFolderName("");
      setIsCreateFolderModalOpen(false);
    } catch (err) {
      setError(err);
      console.error("Error creating folder:", err);
    } finally {
      setIsCreatingFolder(false);
    }
  };

  const handleCancelCreateFolder = () => {
    setNewFolderName("");
    setIsCreateFolderModalOpen(false);
  };

  if (isLoading) {
    return <Loader fullScreen message="Don't Reload Page" />;
  }

  return (
    <DashboardLayout>
      <DashboardNavbar breadCrumbText="All Assignments" />

      {/* Create Folder Modal */}
      <Dialog
        open={isCreateFolderModalOpen}
        onClose={handleCancelCreateFolder}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <MDTypography variant="h5" color="dark">
            Create New Folder
          </MDTypography>
        </DialogTitle>
        <DialogContent>
          <MDBox mt={2}>
            <MDInput
              label="Folder Name"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              fullWidth
              variant="standard"
              onKeyPress={(e) => {
                if (e.key === "Enter" && newFolderName.trim()) {
                  handleCreateFolder();
                }
              }}
            />
          </MDBox>
        </DialogContent>
        <DialogActions>
          <MDBox
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
            px={0.5}
          >
            <MDButton
              variant="outlined"
              color="dark"
              onClick={handleCancelCreateFolder}
              disabled={isCreatingFolder}
            >
              Cancel
            </MDButton>
            <MDButton
              variant="gradient"
              color="info"
              onClick={handleCreateFolder}
              disabled={isCreatingFolder || !newFolderName.trim()}
              sx={{ width: "10rem" }}
            >
              {isCreatingFolder ? <Loader size={20} /> : "Create Folder"}
            </MDButton>
          </MDBox>
        </DialogActions>
      </Dialog>

      <MDBox py={1}>
        <MDBox mb={3}>
          <Card>
            <TableInfo
              tableTitle={
                currentFolderId
                  ? `${
                      breadcrumbPath[breadcrumbPath.length - 1]?.name ||
                      "Folder"
                    }`
                  : "Assignments"
              }
              tableDesc={
                currentFolderId
                  ? "Assignments in this folder"
                  : "Click on folders to navigate or assignments to grade"
              }
              buttonText="Create New Assignment"
              handleClick={() => {
                const url = currentFolderId
                  ? `/assignments?operation=new&folderId=${currentFolderId}`
                  : `/assignments?operation=new`;
                navigate(url);
              }}
              secButtonText={currentFolderId === null ? "Create Folder" : ""}
              handleSecClick={() => setIsCreateFolderModalOpen(true)}
            />

            {/* Breadcrumb Navigation */}
            {(currentFolderId !== null || breadcrumbPath.length > 0) && (
              <MDBox px={3} pt={2.5} pb={1}>
                <MDBox display="flex" alignItems="center" gap={1} mb={1}>
                  <MDButton
                    variant="outlined"
                    color="dark"
                    size="small"
                    onClick={handleBackClick}
                    sx={{ minWidth: "auto", px: 1 }}
                  >
                    <ArrowBackIcon fontSize="small" />
                  </MDButton>
                  <Breadcrumbs separator="›" sx={{ ml: 1 }}>
                    <Chip
                      icon={<HomeIcon />}
                      label="All Assignments"
                      onClick={() => handleBreadcrumbClick(-1)}
                      variant="outlined"
                      size="small"
                      sx={{ cursor: "pointer" }}
                    />
                    {breadcrumbPath.map((folder, index) => (
                      <Chip
                        key={folder.id}
                        icon={<FolderIcon />}
                        label={folder.name}
                        onClick={() => handleBreadcrumbClick(index)}
                        variant={
                          index === breadcrumbPath.length - 1
                            ? "filled"
                            : "outlined"
                        }
                        size="small"
                        sx={{ cursor: "pointer" }}
                      />
                    ))}
                  </Breadcrumbs>
                </MDBox>
              </MDBox>
            )}

            <DataTable
              table={tableData}
              canSearch
              viewData
              onView={(assignment) => {
                // Prevent navigation for folders
                if (assignment.row?.original?.isFolder) {
                  return;
                }
                navigate(`/assignments/${assignment.value}?operation=view`);
              }}
              isRowClickable
              handleRowClick={handleNavigateToGradeAssignment}
              grades
              onGrade={(submission) => {
                // Prevent navigation for folders
                if (submission.row?.original?.isFolder) {
                  return;
                }
                navigate(`/assignments/${submission.value}?operation=grade`);
              }}
              showMenu={true}
            />
          </Card>
        </MDBox>
      </MDBox>
    </DashboardLayout>
  );
}
