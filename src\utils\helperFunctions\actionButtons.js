import React, { useState, useContext } from "react";
import { ApiServiceContext } from "context";
import USER_ROLES from "utils/helperFunctions/USER_ROLES";
import Tooltip from "@mui/material/Tooltip";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import ModeEditOutlineRoundedIcon from "@mui/icons-material/ModeEditOutlineRounded";
import DeleteRoundedIcon from "@mui/icons-material/DeleteRounded";
import Loader from "components/atoms/Loader/Loader";
import MDButton from "components/atoms/MDButton";
import DataTableBodyCell from "examples/Tables/DataTable/DataTableBodyCell";
import MDBox from "components/atoms/MDBox";

export function ActionButtons({
  row,
  onEdit,
  onDelete,
  onView,
  onGrade,
  onUploadSubmission,
  onViewSubmission,
  onPublish,
  publishingStudentId,
  editData,
  deleteData,
  viewData,
  grades,
  uploadSubmission,
  viewSubmission,
  publish,
  isCustomActionButton,
  isActionButtonCentered,
  showMenu,
  onMoveToFolder,
  foldersList,
}) {
  const { apiService } = useContext(ApiServiceContext);
  const status = row?.cells[0]?.row?.values?.status?.toLowerCase();
  const isStudent = apiService?.role === USER_ROLES.STUDENT_ROLE;
  const currentStudentId = row?.cells[0]?.row?.values?.studentId;
  const isPublishing = publishingStudentId === currentStudentId;

  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleMenuClick = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };



  return (
    <DataTableBodyCell
      isVisible={true}
      align={isActionButtonCentered ? "center" : "right"}
    >
      <MDBox display="flex" alignItems="center" gap={1} justifyContent="center">
        {!isStudent && status === "processing" && (
          <MDBox sx={{ width: "4.5rem" }}>
            <Loader loaderColor="info" />
          </MDBox>
        )}
        {publish && status === "graded" && (
          <MDButton
            size="small"
            variant="outlined"
            color="info"
            onClick={(e) => {
              e.stopPropagation();
              onPublish(row?.cells[0]);
            }}
            disabled={isPublishing}
            sx={{ width: "4.5rem" }}
          >
            {isPublishing ? <Loader loaderColor="info" /> : "Publish"}
          </MDButton>
        )}
        {editData && (
          <Tooltip title="Edit">
            <ModeEditOutlineRoundedIcon
              style={{
                height: "20px",
                width: "auto",
                cursor: "pointer",
                color: "#666272",
              }}
              onClick={(e) => {
                e.stopPropagation();
                onEdit(row?.cells[0]);
              }}
            />
          </Tooltip>
        )}
        {viewSubmission &&
          ((isStudent && status === "graded") ||
            (!isStudent &&
              (status === "graded" || status === "published"))) && (
            <MDButton
              size="small"
              variant="outlined"
              color="success"
              onClick={(e) => {
                e.stopPropagation();
                onViewSubmission(row?.cells[0]);
              }}
              sx={{ width: "4.5rem" }}
            >
              View
            </MDButton>
          )}
        {deleteData &&
          (status && !isStudent
            ? status === "failed" ||
              status === "graded" ||
              status === "published"
            : true) &&
          (isCustomActionButton ? (
            <MDButton
              size="small"
              variant="outlined"
              color="error"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(row?.cells[0]);
              }}
              sx={{ width: "4.5rem" }}
            >
              Delete
            </MDButton>
          ) : (
            <Tooltip title="Delete">
              <DeleteRoundedIcon
                style={{
                  cursor: "pointer",
                  height: "20px",
                  width: "auto",
                  color: "#666272",
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(row?.cells[0]);
                }}
              />
            </Tooltip>
          ))}
        {viewData && (
          <MDButton
            size="small"
            variant="outlined"
            color="success"
            onClick={(e) => {
              e.stopPropagation();
              onView(row?.cells[0]);
            }}
            sx={{ width: "4.5rem" }}
          >
            View
          </MDButton>
        )}
        {grades && (
          <MDButton
            size="small"
            variant="outlined"
            color="info"
            onClick={(e) => {
              e.stopPropagation();
              onGrade(row?.cells[0]);
            }}
            sx={{ width: "4.5rem" }}
          >
            Grade
          </MDButton>
        )}
        {uploadSubmission && status === "due" && (
          <MDButton
            size="small"
            variant="outlined"
            color="warning"
            onClick={(e) => {
              e.stopPropagation();
              onUploadSubmission(row?.cells[0]);
            }}
            sx={{ width: "4.5rem" }}
          >
            Upload
          </MDButton>
        )}
        {showMenu && !row?.cells[0]?.row?.original?.isFolder && (
          <>
            <Tooltip title="Move to Folder" placement="top">
              <MoreVertIcon
                style={{
                  height: "20px",
                  width: "auto",
                  cursor: "pointer",
                  color: "#666272",
                }}
                onClick={handleMenuClick}
              />
            </Tooltip>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleMenuClose}
              onClick={(e) => e.stopPropagation()}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "right",
              }}
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
            >
              {foldersList && foldersList.length > 0 ? (
                foldersList.map((folder) => (
                  <MenuItem
                    key={folder.id}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (onMoveToFolder) {
                        onMoveToFolder(row?.cells[0], folder);
                      }
                      handleMenuClose();
                    }}
                  >
                    {folder.name}
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled>
                  No folders available
                </MenuItem>
              )}
            </Menu>
          </>
        )}
      </MDBox>
    </DataTableBodyCell>
  );
}
