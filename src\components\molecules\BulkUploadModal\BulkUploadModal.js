import { Modal } from "components/atoms/Modal/Modal";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDTypography from "components/atoms/MDTypography";
import { useContext, useState } from "react";
import { ApiServiceContext } from "context";
import { toast } from "react-toastify";
import { convertPdfToImages } from "utils/helperFunctions/convertPdfToImages";
import { compressAndUploadFiles } from "utils/helperFunctions/compressAndUploadFiles";
import ProgressBar from "@ramonak/react-progress-bar";
import colors from "assets/theme/base/colors";

export const BulkUploadModal = ({
  isOpen,
  onClose,
  assignmentId,
  onUploadSuccess,
  allStudents,
}) => {
  const [isBulkUploading, setIsBulkUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [processedFiles, setProcessedFiles] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);
  const [currentPhase, setCurrentPhase] = useState("");
  const { apiService } = useContext(ApiServiceContext);

  const handleBulkUpload = async (event) => {
    const files = Array.from(event.target.files);

    // Check if all files are PDFs
    const nonPdfFiles = files.filter((file) => file.type !== "application/pdf");
    if (nonPdfFiles.length > 0) {
      toast.error("Only PDF files are supported for bulk upload");
      return;
    }

    if (files.length === 0) return;

    // Filter files based on studentId match
    const validFiles = files.filter((file) => {
      const studentId = file.name.split(".")[0];
      return allStudents.some((student) => student.studentId === studentId);
    });

    if (validFiles.length === 0) {
      toast.error(
        "No valid files found. Please ensure filenames match student IDs."
      );
      return;
    }

    try {
      setIsBulkUploading(true);
      setTotalFiles(validFiles.length);
      setProcessedFiles(0);
      setUploadProgress(0);

      const BATCH_SIZE = 10; // Process 10 PDFs simultaneously
      const allSubmissionData = [];
      const currentFailedUploads = [];

      // Process files in batches
      for (let i = 0; i < validFiles.length; i += BATCH_SIZE) {
        const batch = validFiles.slice(i, i + BATCH_SIZE);
        setCurrentPhase(
          `Processing PDFs ${i + 1}-${Math.min(
            i + BATCH_SIZE,
            validFiles.length
          )} of ${validFiles.length}...`
        );

        const batchPromises = batch.map(async (pdf, batchIndex) => {
          const studentId = pdf.name.split(".")[0];

          try {
            const pdfImages = await convertPdfToImages(pdf);

            const fileLocationGenerator = (file, index) => {
              const fileExtension = file.name.split(".").pop();
              return `${
                apiService.instituteId
              }/${assignmentId}/${studentId}/page-${
                index + 1
              }.${fileExtension}`;
            };

            const { success, tmpFileList, errorFiles } =
              await compressAndUploadFiles(pdfImages, fileLocationGenerator);

            if (!success || errorFiles.length > 0) {
              currentFailedUploads.push(studentId);
              return null;
            }

            // Update progress for this file
            const currentProcessed = i + batchIndex + 1;
            setProcessedFiles(currentProcessed);
            setUploadProgress((currentProcessed / validFiles.length) * 100);

            return {
              studentId,
              imageUrls: tmpFileList,
            };
          } catch (error) {
            currentFailedUploads.push(studentId);
            return null;
          }
        });

        // Process current batch and wait for completion
        const batchResults = await Promise.all(batchPromises);
        const validResults = batchResults.filter((result) => result !== null);
        allSubmissionData.push(...validResults);
      }

      if (allSubmissionData.length === 0) {
        toast.error(
          "No files were successfully processed. Please check console for details."
        );
        return;
      }

      // Send the bulk submission request
      setCurrentPhase("Finalizing submission...");

      await apiService.uploadBulkSubmissions(assignmentId, {
        submissionData: allSubmissionData,
      });

      if (currentFailedUploads.length > 0) {
        toast.warning(
          `${
            currentFailedUploads.length
          } files failed to process: ${currentFailedUploads.join(", ")}`
        );
      }

      toast.success(
        `Successfully processed ${allSubmissionData.length} submissions`
      );
      onUploadSuccess();
    } catch (error) {
      toast.error(error.message || "Error processing bulk upload");
    } finally {
      onClose();
      setIsBulkUploading(false);
      setUploadProgress(0);
      setProcessedFiles(0);
      setTotalFiles(0);
      setCurrentPhase("");
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      fullWidth
      headingText="Bulk Upload Submissions"
      closeIconVisible={!isBulkUploading}
    >
      <MDBox
        sx={{
          minHeight: "300px",
          display: "flex",
          flexDirection: "column",
          p: 2,
        }}
      >
        {isBulkUploading ? (
          <MDBox
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            flex={1}
          >
            <MDTypography variant="h5" fontWeight="medium" mb={2}>
              Processing Files
            </MDTypography>

            {currentPhase && (
              <MDTypography
                variant="body2"
                color="text.secondary"
                mb={4}
                textAlign="center"
              >
                {currentPhase}
              </MDTypography>
            )}

            <MDBox width="100%" mb={4}>
              <MDBox display="flex" justifyContent="space-between" mb={1}>
                <MDTypography variant="button" color="text">
                  Overall Progress
                </MDTypography>
                <MDTypography variant="button" color="dark">
                  {Math.round(uploadProgress)}%
                </MDTypography>
              </MDBox>
              <ProgressBar
                completed={Math.round(uploadProgress)}
                height="8px"
                isLabelVisible={false}
                bgColor={colors.gradients.info.state}
                baseBgColor="#e9ecef"
                borderRadius="5px"
              />
            </MDBox>

            <MDBox
              sx={{
                width: "100%",
                p: 3,
                borderRadius: "12px",
                backgroundColor: "grey.100",
              }}
            >
              <MDBox display="flex" justifyContent="space-between" mb={2}>
                <MDTypography variant="button" fontWeight="medium">
                  Files Processed
                </MDTypography>
                <MDTypography variant="button" fontWeight="medium">
                  {processedFiles} / {totalFiles}
                </MDTypography>
              </MDBox>

              <MDTypography
                variant="body2"
                color="text.secondary"
                sx={{
                  fontStyle: "italic",
                  textAlign: "center",
                  mt: 2,
                }}
              >
                Please don't close this window while processing
              </MDTypography>
            </MDBox>
          </MDBox>
        ) : (
          <MDBox
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
            height="100%"
            flex={1}
          >
            <MDBox
              display="flex"
              alignItems="center"
              justifyContent="center"
              flex={1}
            >
              <MDButton
                variant="gradient"
                color="info"
                component="label"
                sx={{ width: "200px" }}
              >
                Select PDF Files
                <input
                  type="file"
                  hidden
                  multiple
                  accept="application/pdf"
                  onChange={handleBulkUpload}
                />
              </MDButton>
            </MDBox>

            <MDBox mt={2}>
              <MDTypography variant="h6" color="dark">
                *Note:
              </MDTypography>
              <ul style={{ listStyleType: "disc", paddingLeft: "20px" }}>
                <li>
                  <MDTypography variant="body2" color="dark">
                    Only PDF files are allowed. You can select multiple PDF
                    files to upload
                  </MDTypography>
                </li>
                <li>
                  <MDTypography variant="body2" color="dark">
                    Each PDF filename should match the corresponding student ID
                  </MDTypography>
                </li>
              </ul>
            </MDBox>
          </MDBox>
        )}
      </MDBox>
    </Modal>
  );
};
