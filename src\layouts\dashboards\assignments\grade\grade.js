import React, { useState, useEffect, useContext } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import Card from "@mui/material/Card";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import DataTable from "examples/Tables/DataTable";
import { Modal } from "components/atoms/Modal/Modal";
import { toast } from "react-toastify";
import Loader from "components/atoms/Loader/Loader";
import { ApiServiceContext } from "context";
import { GradesTableHeader } from "constants/GradesTableHeader/GradesTableHeader";
import { TableInfo } from "components/molecules/TableInfo/TableInfo";
import { DeleteModal } from "components/molecules/DeleteModal/DeleteModal";
import { UploadGradeModal } from "components/molecules/UploadGradeModal/UploadGradeModal";
import QRCodeWithUrl from "components/atoms/QRCodeWithUrl/QRCodeWithUrl";
import SUBMISSION_STATUS from "utils/helperFunctions/SUBMISSION_STATUS";
import MDButton from "components/atoms/MDButton";
import { BulkUploadModal } from "components/molecules/BulkUploadModal/BulkUploadModal";

export function Grades() {
  const { apiService } = useContext(ApiServiceContext);
  const location = useLocation();
  const phoneUrl = `app.eddyowl.com${location.pathname}`;
  const { assignmentId } = useParams();
  const [allStudents, setAllStudents] = useState([]);
  const [tableData, setTableData] = useState({});
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isGradeModalOpen, setIsGradeModalOpen] = useState(false);
  const [isMobileSubmissionOpen, setIsMobileSubmissionOpen] = useState(false);
  const [selectedStudentId, setSelectedStudentId] = useState(null);
  const navigate = useNavigate();

  const [isUploadModalLoading, setIsUploadModalLoading] = useState(false);

  const [testDetails, setTestDetails] = useState({});
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [publishingStudentId, setPublishingStudentId] = useState(null);
  const [isPublishingAll, setIsPublishingAll] = useState(false);
  const [hasUnpublishedGradedSubmissions, setHasUnpublishedGradedSubmissions] =
    useState(false);
  const [isBulkUploadModalOpen, setIsBulkUploadModalOpen] = useState(false);

  const fetchStudents = async (assignmentId) => {
    setLoading(true);
    try {
      const result = await apiService.getStudentAsPerExam(assignmentId);
      setAllStudents(result);

      // Calculate if there are any graded but unpublished submissions
      setHasUnpublishedGradedSubmissions(
        result.some((student) => student.status === SUBMISSION_STATUS.GRADED)
      );
    } catch (err) {
      setError(err);
      toast.error("Error fetching students");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStudents(assignmentId);
  }, [assignmentId, apiService]);

  const updateTableColumns = () => {
    const screenWidth = window.innerWidth;
    const baseColumns = GradesTableHeader;

    if (screenWidth < 750) {
      setTableData({
        columns: [baseColumns[0], baseColumns[2], baseColumns[6]],
        rows: allStudents,
      });
    } else {
      setTableData({
        columns: baseColumns,
        rows: allStudents,
      });
    }
  };

  useEffect(() => {
    updateTableColumns();
    window.addEventListener("resize", updateTableColumns);
    return () => {
      window.removeEventListener("resize", updateTableColumns);
    };
  }, [allStudents]);

  const handleUploadSubmission = (studentData) => {
    setSelectedStudentId(studentData?.row?.values?.studentId);
    setIsUploadModalLoading(true);
    setIsGradeModalOpen(true);
  };

  const handleDeleteSubmissionModal = (studentData) => {
    setSelectedStudentId(studentData?.row?.values?.studentId);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteSubmission = async () => {
    setIsDeleteLoading(true);
    try {
      const result = await apiService.deleteStudentSubmission(
        assignmentId,
        selectedStudentId
      );

      if (!result) {
        toast.error("Submission deletion failed");
        return;
      }

      setIsDeleteModalOpen(false);

      setAllStudents((prevStudents) =>
        prevStudents.map((student) =>
          student.studentId === selectedStudentId
            ? { ...student, status: SUBMISSION_STATUS.DUE, studentScore: 0 }
            : student
        )
      );
    } catch (err) {
      setError(err);
      toast.error("Error deleting submission");
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const getTestDetails = async () => {
    try {
      const result = await apiService.getAssignment(assignmentId);
      setTestDetails(result);
    } catch (err) {
      setError(err);
      toast.error("Error In Loading Test Details");
    }
  };

  useEffect(() => {
    getTestDetails();
  }, [assignmentId, apiService]);

  const handlePublishSubmission = async (studentData) => {
    const studentId = studentData?.row?.values?.studentId;
    setPublishingStudentId(studentId);
    try {
      await apiService.publishStudentSubmission(assignmentId, studentId);

      setAllStudents((prevStudents) =>
        prevStudents.map((student) =>
          student.studentId === studentId
            ? { ...student, status: SUBMISSION_STATUS.PUBLISHED }
            : student
        )
      );

      toast.success("Submission published successfully");
    } catch (err) {
      toast.error("Error publishing submission");
    } finally {
      setPublishingStudentId(null);
    }
  };

  const handlePublishAll = async () => {
    setIsPublishingAll(true);
    try {
      await apiService.publishAllSubmissions(assignmentId);

      // Update all graded submissions to published
      setAllStudents((prevStudents) =>
        prevStudents.map((student) =>
          student.status === SUBMISSION_STATUS.GRADED
            ? { ...student, status: SUBMISSION_STATUS.PUBLISHED }
            : student
        )
      );

      toast.success("All submissions published successfully");
    } catch (err) {
      toast.error("Error publishing submissions");
    } finally {
      setIsPublishingAll(false);
    }
  };

  useEffect(() => {
    setHasUnpublishedGradedSubmissions(
      allStudents.some((student) => student.status === SUBMISSION_STATUS.GRADED)
    );
  }, [allStudents]);

  if (isLoading) {
    return <Loader fullScreen message="Don't Reload Page" />;
  }

  const publishAllButton = hasUnpublishedGradedSubmissions ? (
    <MDButton
      variant="outlined"
      color="info"
      onClick={handlePublishAll}
      disabled={isPublishingAll}
      sx={{ minWidth: "8rem" }}
    >
      {isPublishingAll ? <Loader loaderColor="info" /> : "Publish All"}
    </MDButton>
  ) : null;

  return (
    <DashboardLayout>
      <DashboardNavbar breadCrumbText="Grade Assignment" />
      <DeleteModal
        isModalOpen={isDeleteModalOpen}
        setIsModalOpen={setIsDeleteModalOpen}
        handleChange={handleDeleteSubmission}
        title="Delete Submission"
        loader={isDeleteLoading}
      />
      <Modal
        isOpen={isMobileSubmissionOpen}
        onClose={() => setIsMobileSubmissionOpen(false)}
      >
        <MDBox>
          <MDTypography
            variant="h5"
            fontWeight="medium"
            className="text-center"
          >
            Scan QR to upload files from mobile
          </MDTypography>
          <QRCodeWithUrl phoneUrl={phoneUrl} />
        </MDBox>
      </Modal>
      <UploadGradeModal
        assignmentId={assignmentId}
        selectedStudentId={selectedStudentId}
        setSelectedStudentId={setSelectedStudentId}
        isGradeModalOpen={isGradeModalOpen}
        setIsGradeModalOpen={setIsGradeModalOpen}
        setAllStudents={setAllStudents}
        allStudents={allStudents}
        isUploadModalLoading={isUploadModalLoading}
        setIsUploadModalLoading={setIsUploadModalLoading}
      />
      <BulkUploadModal
        isOpen={isBulkUploadModalOpen}
        onClose={() => setIsBulkUploadModalOpen(false)}
        assignmentId={assignmentId}
        onUploadSuccess={() => fetchStudents(assignmentId)}
        allStudents={allStudents}
      />
      <MDBox py={1}>
        <MDBox mb={3} position="relative">
          <Card>
            <TableInfo
              tableTitle={`Submissions for ${testDetails?.name} | ${testDetails?.subjectName}`}
              isCustomTitle
              tableDesc={`Grade ${
                testDetails?.class
              } | ${testDetails?.sectionList?.join(", ")}`}
              isDescBold
              buttonText="Upload from phone"
              handleClick={() => setIsMobileSubmissionOpen(true)}
              secButtonText="View Assignment"
              handleSecClick={() =>
                navigate(`/assignments/${assignmentId}?operation=view`)
              }
              thirdButton={publishAllButton}
              showUploadMultiple={true}
              handleUploadMultiple={() => setIsBulkUploadModalOpen(true)}
            />
            <DataTable
              table={tableData}
              canSearch
              deleteData
              onDelete={handleDeleteSubmissionModal}
              uploadSubmission
              onUploadSubmission={handleUploadSubmission}
              viewSubmission
              onViewSubmission={(submission) => {
                navigate(
                  `/assignments/${assignmentId}/submissions?studentId=${submission.value}`
                );
              }}
              publish
              onPublish={handlePublishSubmission}
              publishingStudentId={publishingStudentId}
              isCustomActionButton
              isActionButtonCentered={false}
            />
          </Card>
        </MDBox>
      </MDBox>
    </DashboardLayout>
  );
}
