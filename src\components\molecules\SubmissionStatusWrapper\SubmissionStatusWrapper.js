import MDBadgeDot from "components/atoms/MDBadgeDot";

const SubmissionStatusWrapper = ({ children }) => {
  const _swichColorCase = (status) => {
    const SUBMISSION_STATUS = status?.props?.cell?.value.toLowerCase()

    switch (SUBMISSION_STATUS) {
      case "due":
        return <MDBadgeDot color={"warning"} badgeContent={children} size="lg" />;
      case "processing":
        return <MDBadgeDot color={"info"} badgeContent={children} size="lg" />;
      case "graded":
        return <MDBadgeDot color={"success"} badgeContent={children} size="lg" />;
      case "failed":
        return <MDBadgeDot color={"error"} badgeContent={children} size="lg" />;
      case "published":
        return <MDBadgeDot color={"info"} badgeContent={children} size="lg" />;
      default:
        return children; 
    }
  };

  return _swichColorCase(children);
};

export default SubmissionStatusWrapper;